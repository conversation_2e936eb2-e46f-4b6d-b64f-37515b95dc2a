﻿$(document).ready(function () {
    // Auto-populate Reference No (simulate auto-calc)
    $('#ReferenceNo').val('REF-' + Math.floor(Math.random() * 1000000));
    // Auto-populate Date/Time
    const now = new Date();

    const day = ('0' + now.getDate()).slice(-2);
    const month = ('0' + (now.getMonth() + 1)).slice(-2);
    const year = now.getFullYear();

    const hours = ('0' + now.getHours()).slice(-2);
    const minutes = ('0' + now.getMinutes()).slice(-2);

    const formatted = `${day}-${month}-${year} ${hours}:${minutes}`;

    $('#DateTime').val(formatted);
    attachRequiredValidation('#ContactPersonId', 'Contact Person is required');
    
    attachRequiredValidation('#FacilityId', 'Facility is required');
    // Initial check on page load
    checkRequiredFields();

    // On page load or modal open
    toggleRecommendationSection();

    // Populate Facility dropdown
    $.get(getAllFacility, function (data) {
        var options = '<option value="">Select Facility</option>';
        data.forEach(function (f) {
            options += `<option value="${f.facilityId}">${f.facilityName}</option>`;
        });
        $('#FacilityId').html(options);
    });

    // Populate Department, Location, TypeOfInspection, Status, Verification with static/dummy data
    var departments = ['HR', 'Maintenance', 'Production'];
    var locations = ['Block A', 'Block B', 'Block C'];
    var types = ['Routine', 'Special', 'Follow-up'];
    var statuses = ['Open', 'In Progress', 'Closed'];
    var verifications = ['Verified', 'Not Verified'];

    //fillDropdown('#DepartmentId', departments);
    fillDropdown('#TypeOfInspection', types);
    var statuses = [
        { id: 1, name: 'Complete' },
        { id: 2, name: 'Pending' },
        { id: 3, name: 'Cannot rectify' }
    ];

    var verifications = [
        { id: 1, name: 'Accept' },
        { id: 2, name: 'Reject' },
        { id: 3, name: 'KIV' }
    ];

    fillDropdownWithId('#Status', statuses);
    fillDropdownWithId('#Verification', verifications);

    function fillDropdown(selector, arr) {
        var options = '<option value="">Select</option>';
        arr.forEach(function (v) { options += `<option value="${v}">${v}</option>`; });
        $(selector).html(options);
    }

    // On Facility change, fetch Action Parties
    $('#FacilityId').change(function () {
        var facilityId = $(this).val();
        if (facilityId) {
            $.get(getUsersByFacilityId + facilityId, function (data) {
                var options = null;
                var contactPerson = '<option value="">Select Contact Person</option>'
                if (data && data.length) {
                    data.forEach(function (ap) {
                        options += `<option value="${ap.userId}">${ap.username}</option>`;
                    });
                }
                $('#ActionPartyId').html(options);

                if (data && data.length) {
                    data.forEach(function (ap) {
                        contactPerson += `<option value="${ap.userId}">${ap.username}</option>`;
                    });
                }
                $('#ContactPersonId').html(contactPerson);
            });
        } else {
            $('#ActionPartyId').html('<option value="">Select Action Party</option>');
            $('#ContactPersonId').html('<option value="">Select Contact Person</option>');
        }
    });



    $('#saveObservation').click(function () {
        // Handle save logic here (grab modal fields, validate, etc.)
        $('#observationModal').modal('hide');
    });
    // Dummy Contact Person (could be fetched based on Action Party)
    //fillDropdown('#ContactPersonId', ['John Doe', 'Jane Smith', 'Other']);

    function fillDropdownWithId(selector, items) {
        var options = '<option value="">Select</option>';
        items.forEach(function (item) {
            options += `<option value="${item.id}">${item.name}</option>`;
        });
        $(selector).html(options);
    }
    // Auto-populate InspectorName from session (simulate)
    $('#InspectorName').val(loggedInUserName || 'Current User');
    $('#inspectionForm').on('submit', async function (e) {
        e.preventDefault();
        var selectedCategory = window.TypeaheadSelectedCategory || null;
        var selectedLocation = window.TypeaheadSelectedLocation || null;

        var observation = tinymce.get('tinyMceExample').getContent();
        var recommendation = tinymce.get('editTinyMceExample').getContent();

        var categoryId = parseInt($('#selectedCategoryId').val()) || 0;
        var categoryName = categoryId === 0 && selectedCategory?.name ? selectedCategory.name.trim() : null;
        var selectedLocationId = $('#selectedLocationId').val();
        var locationText = $('#LocationId').val();

        var Location = selectedLocationId && selectedLocationId.trim() !== ''
            ? selectedLocationId.trim()
            : (locationText?.trim() || null);

        const dto = {
            InspectionId: parseInt($('#InspectionId').val()) || 0,
            FacilityId: parseInt($('#FacilityId').val()) || null,
            Title: $('#Title').val(),
            Description: $('#Description').val(),
            InspectionDate: new Date().toISOString().split('T')[0],
            ReferenceNo: $('#ReferenceNo').val(),
            TypeOfInspection: categoryId,
            TypeOfInspectionName: categoryName,
            InspectionLocation: Location,
            MstInspectionItems: []
        };
        dto.MstInspectionItems = observations.map(obs => ({
            Observation: obs.Observation,
            Recommendation: obs.Recommendation,
            Location: obs.Location,
            ObservationType: parseInt(obs.TypeStatus),
            ContactPersonId: parseInt(obs.ContactPersonId),
            SpecificLocation: obs.Location,
            Status: parseInt(obs.Status),
            ActionPartyId: obs.ActionPartyIds.join(','), // comma-separated
            ContactPersonName: obs.ContactPersonName,

            ObservationMediaUrl: obs.ObservationAttachmentBase64 || '',
            RecommendationMediaUrl: obs.RecommendationAttachmentBase64 || '',
            ObservationMediaName: obs.ObservationAttachmentName || '',
            RecommendationMediaName: obs.RecommendationAttachmentName || '',

            //// Optional fields (if needed):
            //Rectification: '',
            //AfterImagePath: '',
            //CompletionDateTime: null,
            //Verification: 0,
            TypeOfInspection: parseInt($('#selectedCategoryId').val()) || 0,
        }));

        // Send as JSON
        $.ajax({
            url: $(this).attr('data-action'),
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(dto),
            success: function (response) {
                showSuccessToast(response.message);
                    window.location.reload();
            },
            error: function (xhr) {
                const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
                showDangerToast(errorMessage);
            }
        });
    });


    let observations = [];
    let editIndex = -1;

    function renderObservationTable() {
        const tbody = $('#observationTable tbody');
        tbody.empty();

        observations.forEach((item, index) => {
            const statusText = item.Status === 2 ? "Pending" : "Completed";
            const typeText = item.TypeStatus === 1 ? "Positive" : "For Action";
            const observationFull = $('<div>').html(item.Observation || '').text();
            const recommendationFull = $('<div>').html(item.Recommendation || '').text();

            const obsShort = observationFull.substring(0, 100);
            const recShort = recommendationFull.substring(0, 100);

            const showObsToggle = observationFull.length > 100;
            const showRecToggle = recommendationFull.length > 100;
            tbody.append(`
            <tr data-index="${index}">
                <td>${index + 1}</td>
                <td style="max-width: 300px;white-space: normal;">
                 <span id="obs-short-${index}">${obsShort}${showObsToggle ? '...' : ''}</span>
                    <span id="obs-full-${index}" style="display:none;">${observationFull}</span>
                    ${showObsToggle
                    ? `<br><a href="javascript:void(0);" class="text-primary toggle-link" data-type="obs" data-index="${index}">See more</a>`
                    : ''
                    }
                </td>
                <td style="max-width: 300px;white-space: normal;">
                   <span id="rec-short-${index}">${recShort}${showRecToggle ? '...' : ''}</span>
                    <span id="rec-full-${index}" style="display:none;">${recommendationFull}</span>
                    ${showRecToggle
                    ? `<br><a href="javascript:void(0);" class="text-primary toggle-link" data-type="rec" data-index="${index}">See more</a>`
                    : ''
                    }
                </td>
<td>
    <div class="d-flex flex-wrap" style="max-width: 300px;">
        ${item.ActionPartyNames.map((name, i) => {
            const statusLabel = item.Status === 2 ? 'Pending' : 'Completed';
            const badgeColors = ['badge-danger', 'badge-info', 'badge-success', 'badge-warning', 'badge-secondary'];
            const badgeClass = badgeColors[i % badgeColors.length];
            return `<span class="badge ${badgeClass} mr-1 mb-1">${name.trim()}</span>`;
        }).join('')}
    </div>
</td>        <td>
    <span class="badge ${item.Status === 2 ? 'badge-warning' : 'badge-success'}">
        ${statusText}
    </span>
</td>
                <td>
                    <button type="button" class="btn btn-sm btn-warning edit-observation">Edit</button>
                    <button type="button" class="btn btn-sm btn-danger delete-observation">Remove</button>
                </td>
            </tr>
        `);
        });
    }

    $(document).on('click', '.toggle-link', function () {
        const index = $(this).data('index');
        const type = $(this).data('type'); // "obs" or "rec"

        const shortSpan = $(`#${type}-short-${index}`);
        const fullSpan = $(`#${type}-full-${index}`);
        const link = $(this);

        if (fullSpan.is(':visible')) {
            fullSpan.hide();
            shortSpan.show();
            link.text('See more');
        } else {
            fullSpan.show();
            shortSpan.hide();
            link.text('See less');
        }
    });

    $('#observationForm').on('submit', async function (e) {
        e.preventDefault();

        const Observation = tinymce.get('tinyMceExample').getContent();
        const Recommendation = tinymce.get('editTinyMceExample').getContent();
        const Location = $('#ObservationLocationId').val();
        const TypeStatus = $('#TypeStatus').is(':checked') ? 1 : 2;

        const Status = 2;

        const ActionPartyIds = $('#ActionPartyId').val() || [];
        const ActionPartyNames = $('#ActionPartyId option:selected').map(function () {
            return $(this).text();
        }).get();

        const ContactPersonId = $('#ContactPersonId').val();
        const ContactPersonName = $('#ContactPersonId option:selected').text();

        // 📁 Handle Observation attachment
        const observationFileInput = $('#ObservationAttachment')[0];
        let ObservationAttachmentBase64 = '';
        let ObservationAttachmentName = '';

        if (observationFileInput.files.length > 0) {
            const obsFile = observationFileInput.files[0];
            ObservationAttachmentBase64 = await toBase64(obsFile);
            ObservationAttachmentName = obsFile.name;
        } else {
            ObservationAttachmentBase64 = $('#ObservationAttachmentBase64').val();
            ObservationAttachmentName = $('#ObservationAttachment').siblings('.file-upload-info').val();
        }

        // 📁 Handle Recommendation attachment
        const recommendationFileInput = $('#RecommendationAttachment')[0];
        let RecommendationAttachmentBase64 = '';
        let RecommendationAttachmentName = '';

        if (recommendationFileInput.files.length > 0) {
            const recFile = recommendationFileInput.files[0];
            RecommendationAttachmentBase64 = await toBase64(recFile);
            RecommendationAttachmentName = recFile.name;
        } else {
            RecommendationAttachmentBase64 = $('#RecommendationAttachmentBase64').val();
            RecommendationAttachmentName = $('#RecommendationAttachment').siblings('.file-upload-info').val();
        }

        // 🔁 Push to observation array
        const observationItem = {
            Observation,
            Recommendation,
            Location,
            TypeStatus,
            ActionPartyIds,
            ActionPartyNames,
            ContactPersonId,
            ContactPersonName,
            Status,

            // Attachments
            ObservationAttachmentBase64,
            ObservationAttachmentName,
            RecommendationAttachmentBase64,
            RecommendationAttachmentName
        };

        if (editIndex === -1) {
            observations.push(observationItem);
        } else {
            observations[editIndex] = observationItem;
            editIndex = -1;
        }

        // Clear + close modal
        $('#observationModal').modal('hide');
        $('#observationForm')[0].reset();
        tinymce.get('tinyMceExample').setContent('');
        tinymce.get('editTinyMceExample').setContent('');
        $('#ActionPartyId').val(null).trigger('change');
        $('#ObservationAttachment').val('');
        $('#RecommendationAttachment').val('');
        renderObservationTable();
    });

    // Open modal for Add
    $('#addObservationBtn').on('click', function () {
        editIndex = -1;
        $('#observationForm')[0].reset();
        tinymce.get('tinyMceExample').setContent('');
        tinymce.get('editTinyMceExample').setContent('');
        $('#ActionPartyId').val(null).trigger('change');
        // Clear uploaded file names
        $('#ObservationAttachment').siblings('.file-upload-info').val('');
        $('#RecommendationAttachment').siblings('.file-upload-info').val('');
        attachRequiredValidation('#ActionPartyId', 'Action Party is required');
        $('#observationModal').modal('show');
    });

    // Edit
    $('#observationTable').on('click', '.edit-observation', function () {
        const index = $(this).closest('tr').data('index');
        const obs = observations[index];
        editIndex = index;

        $('#ObservationLocationId').val(obs.Location);
        $('#TypeStatus').prop('checked', obs.TypeStatus == 1);

        $('#ContactPersonId').val(obs.ContactPersonId);
        $('#ActionPartyId').val(obs.ActionPartyIds).trigger('change');

        tinymce.get('tinyMceExample').setContent(obs.Observation);
        tinymce.get('editTinyMceExample').setContent(obs.Recommendation);

        // Show uploaded filenames if available
        // Attachments - Name + Hidden Base64
        if (obs.ObservationAttachmentName) {
            $('#ObservationAttachment').siblings('.file-upload-info').val(obs.ObservationAttachmentName);
            $('#ObservationAttachmentBase64').val(obs.ObservationAttachmentBase64 || '');
        } else {
            $('#ObservationAttachment').siblings('.file-upload-info').val('');
            $('#ObservationAttachmentBase64').val('');
        }

        if (obs.RecommendationAttachmentName) {
            $('#RecommendationAttachment').siblings('.file-upload-info').val(obs.RecommendationAttachmentName);
            $('#RecommendationAttachmentBase64').val(obs.RecommendationAttachmentBase64 || '');
        } else {
            $('#RecommendationAttachment').siblings('.file-upload-info').val('');
            $('#RecommendationAttachmentBase64').val('');
        }

        $('#observationModal').modal('show');
    });


    // Delete
    $('#observationTable').on('click', '.delete-observation', function () {
        const index = $(this).closest('tr').data('index');
        observations.splice(index, 1);
        renderObservationTable();
    });


});
$('#observationUploadBtn').click(() => $('#ObservationAttachment').click());

$('#ObservationAttachment').change(function () {
    const file = this.files[0];
    if (file) {
        $('#ObservationFileName').val(file.name);
        $('#removeObservationFile').removeClass('d-none');

        const reader = new FileReader();
        reader.onload = function (e) {
            $('#ObservationPreview').attr('src', e.target.result);
            $('#ObservationPreviewContainer').removeClass('d-none');
        };
        reader.readAsDataURL(file);
    }
});

$('#removeObservationFile').click(function () {
    $('#ObservationAttachment').val('');
    $('#ObservationFileName').val('');
    $('#removeObservationFile').addClass('d-none');
    $('#ObservationPreviewContainer').addClass('d-none');
    $('#ObservationPreview').attr('src', '');
});

// RECOMMENDATION
$('#recommendationUploadBtn').click(() => $('#RecommendationAttachment').click());

$('#RecommendationAttachment').change(function () {
    const file = this.files[0];
    if (file) {
        $('#RecommendationFileName').val(file.name);
        $('#removeRecommendationFile').removeClass('d-none');

        const reader = new FileReader();
        reader.onload = function (e) {
            $('#RecommendationPreview').attr('src', e.target.result);
            $('#RecommendationPreviewContainer').removeClass('d-none');
        };
        reader.readAsDataURL(file);
    }
});

$('#removeRecommendationFile').click(function () {
    $('#RecommendationAttachment').val('');
    $('#RecommendationFileName').val('');
    $('#removeRecommendationFile').addClass('d-none');
    $('#RecommendationPreviewContainer').addClass('d-none');
    $('#RecommendationPreview').attr('src', '');
});

function toggleRecommendationSection() {
    const isPositive = $('#TypeStatus').is(':checked');
    if (isPositive) {
        $('#recommendationSection').hide();
    } else {
        $('#recommendationSection').show();
    }
}



// Listen for toggle switch change
$('#TypeStatus').change(function () {
    toggleRecommendationSection();
});

// Helper to convert file to base64
function toBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}
