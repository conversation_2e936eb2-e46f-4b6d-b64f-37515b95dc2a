// Permission handling functionality
const PermissionManager = {
    // Get permissions from localStorage
    getPermissions: function() {
        const permissions = localStorage.getItem('permissions');
        return permissions ? JSON.parse(permissions) : [];
    },

    // Find permission by controller and action
    findPermission: function(controllerName, actionName) {
        const permissions = this.getPermissions();
        
        // Recursive function to search through permission tree
        const searchPermissions = (items) => {
            for (const item of items) {
                if (item.controllerName === controllerName && item.actionName === actionName) {
                    return item;
                }
                if (item.children && item.children.length > 0) {
                    const found = searchPermissions(item.children);
                    if (found) return found;
                }
            }
            return null;
        };

        return searchPermissions(permissions);
    },

    // Check if user has specific permission
    hasPermission: function(controllerName, actionName, permissionType) {
        const permission = this.findPermission(controllerName, actionName);
        return permission ? permission[permissionType] : false;
    },

    // Apply permissions to page elements
    applyPermissions: function(controllerName, actionName) {
        const permission = this.findPermission(controllerName, actionName);
        
        if (!permission) {
            console.warn('No permissions found for:', controllerName, actionName);
            return;
        }

        // Handle view permission
        if (!permission.canView) {
            // Hide the entire content if user can't view
            document.querySelector('.card-body').style.display = 'none';
            return;
        }

        // Handle create permission
        const createElements = document.querySelectorAll('[data-permission="create"]');
        createElements.forEach(element => {
            element.style.display = permission.canCreate ? '' : 'none';
        });

        // Handle edit permission
        const editElements = document.querySelectorAll('[data-permission="edit"]');
        editElements.forEach(element => {
            element.style.display = permission.canEdit ? '' : 'none';
        });

        // Handle delete permission
        const deleteElements = document.querySelectorAll('[data-permission="delete"]');
        deleteElements.forEach(element => {
            element.style.display = permission.canDelete ? '' : 'none';
        });

        // Disable form inputs if user can't edit
        if (!permission.canEdit) {
            const formInputs = document.querySelectorAll('input, select, textarea');
            formInputs.forEach(input => {
                input.disabled = true;
            });
        }
    }
};

// Initialize permissions when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // For Add User page
    if (window.location.pathname.includes('/User/AddUser')) {
        PermissionManager.applyPermissions('User', 'AddUser');
    }
}); 