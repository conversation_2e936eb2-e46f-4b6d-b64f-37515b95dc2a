﻿$(document).ready(function () {
    initializeDataTable('#order-listing');

    const email = "email";
    const passwordInput = $("#password");
    const editPasswordInput = $("#editUserPassword");
    const toggleIcon = $("#togglePassword");
    const editToggleIcon = $("#editTogglePassword");
    const feedback = $("#passwordFeedback");
    const editPasswordFeedback = $("#editPasswordFeedback");

    attachRequiredValidation('#lastName', 'Last Name is required');
    attachRequiredValidation('#usersEmail', 'Email is required', email);
    attachRequiredValidation('#FirstName', 'First Name is required');
    attachRequiredValidation('#RoleSelect', 'Role is required');
    attachRequiredValidation('#password', 'Password is required', 'password');
    // Initial check on page load
    checkRequiredFields();
 
    //passwordInput.on("input", function () {
    //    const password = passwordInput.val();

    //    if (password.length < 8 || password.length > 12) {
    //        feedback.text(Messages.PasswordTooShort).removeClass("text-success").addClass("text-danger");
    //    } else if (!/[A-Za-z]/.test(password)) {
    //        feedback.text(Messages.PasswordNoLetter).removeClass("text-success").addClass("text-danger");
    //    } else if (!/[\d\W]/.test(password)) {
    //        feedback.text(Messages.PasswordNoSpecialOrDigit).removeClass("text-success").addClass("text-danger");
    //    } else {
    //        feedback.text(Messages.PasswordValid).removeClass("text-danger").addClass("text-success");
    //    }
    //});
    editPasswordInput.on("input", function () {
        const password = editPasswordInput.val();

        if (password.length < 8 || password.length > 12) {
            editPasswordFeedback.text(Messages.PasswordTooShort).removeClass("text-success").addClass("text-danger");
        } else if (!/[A-Za-z]/.test(password)) {
            editPasswordFeedback.text(Messages.PasswordNoLetter).removeClass("text-success").addClass("text-danger");
        } else if (!/[\d\W]/.test(password)) {
            editPasswordFeedback.text(Messages.PasswordNoSpecialOrDigit).removeClass("text-success").addClass("text-danger");
        } else {
            editPasswordFeedback.text(Messages.PasswordValid).removeClass("text-danger").addClass("text-success");
        }
    });
    toggleIcon.on("click", function () {
        const isPassword = passwordInput.attr("type") === "password";
        passwordInput.attr("type", isPassword ? "text" : "password");
        toggleIcon.toggleClass("fa-eye fa-eye-slash");
    });
    editToggleIcon.on("click", function () {
        const isPassword = editPasswordInput.attr("type") === "password";
        editPasswordInput.attr("type", isPassword ? "text" : "password");
        editToggleIcon.toggleClass("fa-eye fa-eye-slash");
    });

    $('input, textarea, select').on('input change', function () {
        $(this).removeClass('input-validation-error');
        $(this).siblings('.field-validation-error').text('');
    });

    // Dropify/file clear
    $('input[type="file"]').on('change', function () {
        $(this).closest('.form-group').find('.field-validation-error').text('');
        $(this).removeClass('input-validation-error');
    });

    $('.js-example-basic-multiple').select2();

    $('form.form-sample').submit(function (e) {
        e.preventDefault();
        const form = $(this);

        // Run client-side validation first
        if (!form.valid()) {
            // Trigger validation manually on all required fields
            form.find('[data-required="true"]').each(function () {
                $(this).trigger('blur');
            });
            return; // Stop submission if validation fails
        }

        // Clear all previous validation errors
        $('.field-validation-error').text('');
        $('.input-validation-error').removeClass('input-validation-error');
        // === Step 2: Prepare FormData ===
        const formData = new FormData(this);
        formData.set("IsSsoUser", $('input[name="IsSsoUser"]').is(':checked'));
        formData.set("IsActive", $('input[name="IsActive"]').is(':checked'));

        // Handle multiple facility selection
        formData.delete("FacilityIds");
        const selectedFacilities = $('select[name="FacilityIds"]').val();
        if (selectedFacilities && selectedFacilities.length) {
            selectedFacilities.forEach(function (val) {
                formData.append("FacilityIds", val);
            });
        }

        // === Step 3: Submit via AJAX ===
        $.ajax({
            url: createUser,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (res) {
                showSuccessToast(res.message || Messages.SuccessRegister);
                $('form.form-sample')[0].reset();
                $('.js-example-basic-multiple').val(null).trigger('change');
                $('.dropify').dropify(); // Reinitialize if needed
                window.location.reload(); // Reloads the current page

            },
            error: function (xhr) {
                const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
                showDangerToast(errorMessage);
            }
        });
    });
});
// Remove row

let facilityRoles = [];


function onFacilityChange(index, selectElement) {
    const newFacilityId = $(selectElement).val();
    facilityRoles[index].facilityId = newFacilityId;
    console.log(`Facility changed to ${newFacilityId}`);
}

// Role change event handler
function onRoleChange(index, selectElement) {
    const newRoleId = $(selectElement).val();
    facilityRoles[index].roleId = newRoleId;
    console.log(`Role changed to ${newRoleId}`);
}



function checkFormValidity() {
    let allValid = true;

    $('form.form-sample [data-required="true"]').each(function () {
        const $input = $(this);
        if ($input.hasClass('is-invalid')) {
            allValid = false;
        }
    });

    $('form.form-sample button[type="submit"]').prop('disabled', !allValid);
}
function renderFacilityRoleTable() {

    const tbody = $('#facilityRoleTable tbody');
    const thead = $('#facilityRoleTable thead');
    tbody.empty();
    thead.empty();

    // ✅ Always render both headers
    let headerHtml = `
        <tr>
           <th>Role</th>
            <th>Facility</th>
            <th>Action</th>
        </tr>`;
    thead.append(headerHtml);

    facilityRoles.forEach((item, index) => {
        const isAppOrDepAdmin = item.roleId === 1 || item.roleId === 5;

        // Role dropdown with default
        const roleOptions = [
            `<option value="">-- Select Role --</option>`,
            ...allRoles.map(role => {
                const selected = role.roleId === item.roleId ? 'selected' : '';
                return `<option value="${role.roleId}" ${selected}>${role.roleName}</option>`;
            })
        ].join('');

        // Facility dropdown with default
        const facilityOptions = [
            `<option value="">-- Select Facility --</option>`,
            ...allFacilities.map(fac => {
                const selected = fac.facilityId === item.facilityId ? 'selected' : '';
                return `<option value="${fac.facilityId}" ${selected}>${fac.facilityName}</option>`;
            })
        ].join('');

        const facilityHtml = !isAppOrDepAdmin
            ? `<select class="form-control facility-select">${facilityOptions}</select>`
            : `<span class="text-muted">N/A</span>`;

        const rowHtml = `
        <tr data-index="${index}">
            <td>
                <select class="form-control role-select">
                    ${roleOptions}
                </select>
            </td>
            <td class="facility-cell">
                ${facilityHtml}
            </td>
        
            <td>
                <button class="btn btn-sm btn-danger removeBtn" data-index="${index}">Remove</button>
            </td>
            <input type="hidden" class="userRoleConfigId" value="${item.userRoleConfigId || 0}" />
        </tr>`;
        tbody.append(rowHtml);
    });

    // Remove button
    $('.removeBtn').click(function () {
        syncTableToDataModel(); // Save selections
        const index = $(this).data('index');
        removeFacilityRole(index);
    });

    $('.role-select').change(function () {
        const selectedRoleId = parseInt($(this).val());
        const row = $(this).closest('tr');
        const facilityCell = row.find('.facility-cell');

        if (selectedRoleId === 1 || selectedRoleId === 5) {
            // Remove dropdown and show N/A
            facilityCell.html(`<span class="text-muted">N/A</span>`);
        } else {
            // Check if a facility was already selected
            const existingSelect = facilityCell.find('select');
            const selectedFacilityId = existingSelect.length ? existingSelect.val() : null;

            // Create dropdown with all facility options
            const facilityOptions = allFacilities.map(fac => {
                const isSelected = selectedFacilityId == fac.facilityId ? 'selected' : '';
                return `<option value="${fac.facilityId}" ${isSelected}>${fac.facilityName}</option>`;
            }).join('');

            facilityCell.html(`<select class="form-control facility-select">${facilityOptions}</select>`);
        }
    });

}
function syncTableToDataModel() {
    $('#facilityRoleTable tbody tr').each(function (index) {
        const roleId = parseInt($(this).find('.role-select').val()) || '';
        const facilitySelect = $(this).find('.facility-select');
        const facilityId = facilitySelect.length > 0 ? parseInt(facilitySelect.val()) || '' : '';

        facilityRoles[index].roleId = roleId;
        facilityRoles[index].facilityId = facilityId;
    });
}

// Initial render of the table
$(document).ready(function () {
    renderFacilityRoleTable();
});
let deletedRoleIds = []; // This will track deleted mappings

// Remove row
function removeFacilityRole(index) {
    const removedItem = facilityRoles[index];

    // If it had a DB ID, add it to deletedRoleIds
    if (removedItem.userRoleConfigId && removedItem.userRoleConfigId > 0) {
        deletedRoleIds.push(removedItem.userRoleConfigId);
    }
    // Remove the role from the facilityRoles array
    facilityRoles.splice(index, 1);
    renderFacilityRoleTable(); // Re-render the table after removing the row
}

// Add New Row (Append a blank row to facilityRoles)
$('#addRowBtn').click(function () {
    syncTableToDataModel(); // Save current selections

    facilityRoles.push({ facilityId: '', roleId: '' });
    renderFacilityRoleTable(); // Re-render the table to show the new row
    //$('#facilityRoleTable tbody tr').each(function (index) {
    //    const roleId = parseInt($(this).find('.role-select').val());
    //    const facilityId = parseInt($(this).find('.facility-select').val());

    //    // Only update if index exists in facilityRoles
    //    if (facilityRoles[index]) {
    //        facilityRoles[index].roleId = isNaN(roleId) ? '' : roleId;
    //        facilityRoles[index].facilityId = isNaN(facilityId) ? '' : facilityId;
    //    }
    //});
});

$('#saveEditUserBtn').click(function () {
    const userData = {
        userId: parseInt($('#editUserId').val()) || 0,
        email: $('#editUserEmail').val(),
        password: $('#editUserPassword').val(),
        firstName: $('#editFirstName').val(),
        lastName: $('#editLastName').val(),
        bio: $('#editBio').val(),
        isSsoUser: $('#editIsSsoUser').is(':checked'),
        isActive: $('#editIsActive').is(':checked'),
        facilityRoleConfigs: [],
        RemovedUserRoleConfigIds: deletedRoleIds,
        language: parseInt($('#editLanguage').val()),
        contactNumber: $('#editContact').val(),  
        employeeCode : $('#editEmpCode').val(),
        existingProfileImageUrl: null
    };

    // Collect Facility-Role rows

    $('#facilityRoleTable tbody tr').each(function () {
        const facilityId = $(this).find('.facility-select').val();
        const roleId = $(this).find('.role-select').val();
        const userRoleConfigId = $(this).find('.userRoleConfigId').val();

        userData.facilityRoleConfigs.push({
            userRoleConfigId: parseInt(userRoleConfigId) || 0,
            facilityId: parseInt(facilityId),
            roleId: parseInt(roleId)
        });
    });

    // If you want to support image upload, you must send FormData instead
    const formData = new FormData();

    const profileImageFile = $('#editProfileImageInput')[0].files[0];
    if (profileImageFile) {
        formData.append("profileImage", profileImageFile);
    } else {
        // Use existing path if new file is not selected
        userData.existingProfileImageUrl = $('#ExistingThumbnailPath').val(); // Send existing file path to backend
    }
    formData.append("userJson", JSON.stringify(userData));


    $.ajax({
        url: saveUserWithRoles,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function (res) {
            showSuccessToast(res.message);
            $('#editUserModal').modal('hide');
            window.location.reload(); // Reloads the current page
        },
        error: function (xhr) {
            const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
            showDangerToast(errorMessage);
        }
    });
});

$('.edit-user-btn').click(function () {
    const userId = $(this).data('user-id');

    $.ajax({
        url: getUserById,
        type: 'GET',
        data: { id: userId },
        success: function (user) {
            $('#editUserId').val(user.userId);
            $('#editUserEmail').val(user.email);
            $('#editUserPassword').val(user.password);
            $('#editFirstName').val(user.firstName);
            $('#editLastName').val(user.lastName);
            $('#editBio').val(user.bio);
            $('#editIsSsoUser').prop('checked', user.isSsoUser);
            $('#editIsActive').prop('checked', user.isActive);
            $('#editLanguage').val(user.language); 
            $('#editContact').val(user.contactNumber); 
            if (user.employeeCode) {
                $('#editEmpCode').val(user.employeeCode).prop('readonly', true);
            } else {
                $('#editEmpCode').val('').prop('readonly', false);
            }
            if (user.profileImageUrl) {
                const cleanUrl = user.profileImageUrl.startsWith('/')
                    ? user.profileImageUrl.substring(1)
                    : user.profileImageUrl;

                $('#editProfileImagePreview').attr('src', cleanUrl);
                $('#editProfileImagePreviewContainer').removeClass('d-none');
                $('#removeEditProfileImageBtn').removeClass('d-none');
                $('#ExistingThumbnailPath').val(cleanUrl || '');

                $('#editProfileImageFileName').val(user.profileImageUrl.split('/').pop());
            } else {
                // If no image, reset preview
                $('#editProfileImagePreview').attr('src', '');
                $('#editProfileImagePreviewContainer').addClass('d-none');
                $('#editProfileImageFileName').val('');
            }

            // Clear the input file field in case of prior value
            $('#editProfileImageInput').val('');
            facilityRoles = user.facilityRoleConfigs || [];
            renderFacilityRoleTable();
            // Show the modal
            $('#editUserModal').modal('show');
        },
        error: function () {
            alert("Failed to load user data.");
        }
    });
});
$('#uploadProfileImageBtn').on('click', function () {
    $('#ProfileImageUpload').click();
});

// On file select
$('#ProfileImageUpload').on('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();

        reader.onload = function (e) {
            $('#ProfileImagePreview').attr('src', e.target.result);
            $('#ProfileImagePreviewContainer').removeClass('d-none');
            $('#removeProfileImageBtn').removeClass('d-none');
            $('#ProfileImageFileName').val(file.name);
        };

        reader.readAsDataURL(file);
    }
});

// Remove image
$('#removeProfileImageBtn').on('click', function () {
    $('#ProfileImageUpload').val('');
    $('#ProfileImagePreview').attr('src', '').hide();
    $('#ProfileImagePreviewContainer').addClass('d-none');
    $('#ProfileImageFileName').val('');
    $(this).addClass('d-none');
});

$('#editProfileImageBtn').on('click', function () {
    $('#editProfileImageInput').click();
});
// Edit image preview
$('#editProfileImageInput').on('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            $('#editProfileImagePreview').attr('src', e.target.result);
            $('#editProfileImagePreviewContainer').removeClass('d-none');
            $('#removeEditProfileImageBtn').removeClass('d-none');

            $('#editProfileImageFileName').val(file.name);
        };
        reader.readAsDataURL(file);
    }
});

// Remove button
$('#removeEditProfileImageBtn').click(function () {
    $('#editProfileImageInput').val('');
    $('#editProfileImagePreview').attr('src', '');
    $('#editProfileImagePreviewContainer').addClass('d-none');
    $('#editProfileImageFileName').val('');
    $(this).addClass('d-none');

});

function toggleFacilityField() {
    const selectedRoleId = $('#RoleSelect').val();
    const $facilityField = $('#selectFacility');

    if (selectedRoleId === "1" || selectedRoleId === "5") {
        // Hide field and clear selection
        $facilityField.val(null).trigger('change'); // clears multiselect
        $facilityField.closest('.col-md-6').hide();
    } else {
        // Show field
        $facilityField.closest('.col-md-6').show();
    }
}

// Initial check on page load
toggleFacilityField();

// Listen to role changes
$('#RoleSelect').change(function () {
    toggleFacilityField();
});
