﻿$(document).ready(function () {
 
    // Function to load facilities into the dropdown
    function loadFacilities() {
        $.ajax({
            url: getAllFacilitiesUrl,
            type: 'GET',
            success: function (response) {
          
                    var facilities = response;
                    var facilityDropdown = $('#facilityId');
                    facilityDropdown.empty();
                    facilityDropdown.append($('<option>', {
                        value: '',
                        text: 'Select Facility'
                    }));
                    facilities.forEach(function (facility) {
                        facilityDropdown.append($('<option>', {
                            value: facility.facilityId,
                            text: facility.facilityName
                        }));
                    });


                var editFacilityId = $('#editFacilityId');
                editFacilityId.empty();
                editFacilityId.append($('<option>', {
                    value: '',
                    text: 'Select Facility'
                }));
                facilities.forEach(function (facility) {
                    editFacilityId.append($('<option>', {
                        value: facility.facilityId,
                        text: facility.facilityName
                    }));
                });
            },
            error: function (xhr, status, error) {
                showDangerToast('An error occurred while loading facilities.');
                console.error(xhr.responseText);
            }
        });
    }

    // Load facilities on page load
    loadFacilities();
    loadFeedbackDetails();
    function loadFeedbackDetails() {
        const table = $('#feedBack-listing');
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().clear().destroy();
        }
        const tbody = table.find('tbody');
        tbody.empty();
        $.ajax({
            url: getFeedback,
            type: 'GET',
            success: function (response) {
                var newsletters = response || [];
                var tbody = $('#feedBack-listing tbody');
                tbody.empty();
                newsletters.data.forEach(function (feedBack, index) {
                    const statusBadge = feedBack.status === 0
                        ? '<label class="badge badge-success">Open</label>'
                        : '<label class="badge badge-danger">Closed</label>';
                    //const actionButton = feedBack.isActive
                    //    ? `<button type="button" class="btn btn-sm btn-outline-warning btn-toggle-active" title="Deactivate" data-newsletter-id="${feedBack.newsletterId}">
                    //         <i class="mdi mdi-close-circle-outline"></i>
                    //       </button>`
                    //    : `<button type="button" class="btn btn-sm btn-outline-success btn-toggle-active" title="Activate" data-newsletter-id="${feedBack.newsletterId}">
                    //         <i class="mdi mdi-check-circle-outline"></i>
                    //       </button>`;
             

                    const actionButton = feedBack.status === 0
                        ? `<button type="button" class="btn btn-sm btn-outline-primary btn-edit-feedback" title="Edit" data-newsletter-id="${feedBack.feedbackId}">
                        <i class="mdi mdi-pencil"></i>
                    </button>`
                        : `<button type="button" class="btn btn-sm btn-outline-secondary btn-preview-feedback"
                            data-feedbackid="${feedBack.feedbackId}"
                            data-name="${feedBack.name}"
                            data-title="${feedBack.title}"
                            data-facilityname="${feedBack.facilityName}"
                            data-description='${feedBack.description || ''}'
                            data-filepath="${feedBack.filePath || ''}"
                            data-status="${feedBack.status}"
                            data-response="${feedBack.response || ''}">
                            <i class="mdi mdi-eye"></i>
                       </button>`;


                    var row = `
                        <tr>
                            <td>${index + 1}</td>
                               <td>${feedBack.name || ''}</td>
                            <td>${feedBack.title || ''}</td>
                            <td>${(feedBack.description || '').split(' ').slice(0, 6).join(' ')}${(feedBack.description || '').split(' ').length > 6 ? '...' : ''}</td>
                                                        <td>${feedBack.date || ''}</td>
<td>${statusBadge}</td>
                            <td>${actionButton}</td>
                        </tr>
                    `;
                    tbody.append(row);
                });
                if (typeof initializeDataTable === 'function') initializeDataTable(table);
            },
            error: function (xhr) {
                let msg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Error loading newsletters.';
                showDangerToast(msg);
            }
        });
    }   
    let previewFormData = {}; // store data for submit after preview

    $('#createFeedbackForm').submit(async function (event) {
        event.preventDefault();

        const fileInput = $('#ProfileImageUpload')[0];
        let base64File = null;
        let fileName = null;
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            base64File = await toBase64(file);
            fileName = file.name;
        }

        const descriptionContent = tinymce.get('tinyMceExample').getContent();
        const facilityName = $('#facilityId option:selected').text();

        previewFormData = {
            Name: $('#Name').val(),
            FacilityId: parseInt($('#facilityId').val()),
            Title: $('#Title').val(),
            Description: descriptionContent,
            FilePath: base64File,
            FileName: fileName,
            FacilityName: facilityName
        };

        // Populate modal
        $('#previewName').text(previewFormData.Name);
        $('#previewTitle').text(previewFormData.Title);
        $('#previewFacility').text(previewFormData.FacilityName);
        $('#previewDescription').html(previewFormData.Description || '(No Description)');
        $('#previewFile').html(fileName ? `<span class="badge badge-info">${fileName}</span>` : '(No File)');

        $('#feedbackPreviewModal').modal('show');
    });
    $('#confirmSubmitBtn').click(function () {
        $.ajax({
            url: createFeedbackUrl,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(previewFormData),
            success: function (response) {
                $('#feedbackPreviewModal').modal('hide');
                showSuccessToast('Feedback created successfully!');
                $('#createFeedbackForm')[0].reset();
                tinymce.get('tinyMceExample').setContent('');
                $('#ProfileImageUpload').val('');
                $('#ProfileImagePreview').attr('src', '').hide();
                $('#ProfileImagePreviewContainer').addClass('d-none');
                $('#ProfileImageFileName').val('');
                loadFeedbackDetails();
            },
            error: function (xhr, status, error) {
                $('#feedbackPreviewModal').modal('hide');
                showDangerToast('An error occurred while creating feedback.');
                console.error(xhr.responseText);
            }
        });
    });

    // 🔁 Preview or Update button handler
    $(document).on('click', '.btn-preview-feedback', function () {
        const $btn = $(this);
        $('#previewNameDetails').text($btn.data('name'));
        $('#previewTitleDetails').text($btn.data('title'));
        $('#previewFacilityDetails').text($btn.data('facilityname'));
        $('#previewDescriptionDetails').html($btn.data('description') || 'N/A');

        const filePath = $btn.data('filepath');
        const filePreview = filePath
            ? `<a href="${filePath}" class="btn btn-sm btn-outline-success" download><i class="fa fa-download mr-1"></i>Download File</a>`
            : '<span class="text-muted">No document uploaded</span>';
        $('#previewFileDetails').html(filePreview);

        $('#previewStatusDetails').text($btn.data('status') === 0 ? 'Open' : 'Closed');
        $('#previewResponseDetails').text($btn.data('response') || 'N/A');

        $('#PreviewModal').modal('show');
    });
    function toBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }
     


    // Handle edit button click
    $(document).on('click', '.btn-edit-feedback', function() {
        const feedbackId = $(this).data('newsletter-id');
        loadFeedbackDetailsById(feedbackId);
    });

    // Function to load feedback details by ID
    function loadFeedbackDetailsById(feedbackId) {
        $.ajax({
            url: `${getFeedbackById}${feedbackId}`,
            type: 'GET',
            success: function(response) {
                if (response.data) {
                    const feedback = response.data;
                    $('#editFeedbackId').val(feedback.feedbackId);
                    $('#editName').val(feedback.name);
                    $('#editTitle').val(feedback.title);
                    $('#editFacilityId').val(feedback.facilityId);
                    tinymce.get('editTinyMceExample').setContent(feedback.description || '');
                    
                    if (feedback.filePath) {
                        const cleanUrl = feedback.filePath.startsWith('/')
                            ? feedback.filePath.substring(1)
                            : feedback.filePath;

                        $('#editProfileImagePreview').attr('src', cleanUrl);
                        $('#editProfileImagePreviewContainer').removeClass('d-none');
                        $('#removeEditProfileImageBtn').removeClass('d-none');
                        $('#ExistingThumbnailPath').val(cleanUrl || '');

                        $('#editProfileImageFileName').val(feedback.filePath.split('/').pop());
                    } else {
                        // If no image, reset preview
                        $('#editProfileImagePreview').attr('src', '');
                        $('#editProfileImagePreviewContainer').addClass('d-none');
                        $('#editProfileImageFileName').val('');
                    }

                    $('#editFeedbackModal').modal('show');
                }
            },
            error: function(xhr) {
                showDangerToast('Error loading feedback details');
                console.error(xhr.responseText);
            }
        });
    }

    // Handle save changes button click
    $('#saveEditBtn').click(async function() {
   
        const descriptionContent = tinymce.get('editTinyMceExample').getContent();
        const facilityName = $('#editFacilityId option:selected').text();
        const fileInput = $('#editProfileImageInput')[0];
        let base64File = null;
        let fileName = null;
        const editFormData = {
            FeedbackId: parseInt($('#editFeedbackId').val()),
            Name: $('#editName').val(),
            FacilityId: parseInt($('#editFacilityId').val()),
            Title: $('#editTitle').val(),
            Description: descriptionContent,
            FilePath: base64File,
            FileName: fileName,
            FacilityName: facilityName,
            ExistingDocUrl: null
        };


        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            editFormData.filePath = await toBase64(file);
            editFormData.fileName = file.name;
        } else {
            // Use existing path if new file is not selected
            editFormData.ExistingDocUrl = $('#ExistingThumbnailPath').val(); // Send existing file path to backend
        }

        $.ajax({
            url: createFeedbackUrl,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(editFormData),
            success: function(response) {
                $('#editFeedbackModal').modal('hide');
                showSuccessToast('Feedback updated successfully!');
                loadFeedbackDetails(); // Reload the table
            },
            error: function(xhr, status, error) {
                showDangerToast('An error occurred while updating feedback.');
                console.error(xhr.responseText);
            }
        });
    });
}); 

$('#uploadProfileImageBtn').on('click', function () {
    $('#ProfileImageUpload').click();
});

// On file select
$('#ProfileImageUpload').on('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();

        reader.onload = function (e) {
            $('#ProfileImagePreview').attr('src', e.target.result);
            $('#ProfileImagePreviewContainer').removeClass('d-none');
            $('#removeProfileImageBtn').removeClass('d-none');
            $('#ProfileImageFileName').val(file.name);
        };

        reader.readAsDataURL(file);
    }
});

// Remove image
$('#removeProfileImageBtn').on('click', function () {
    $('#ProfileImageUpload').val('');
    $('#ProfileImagePreview').attr('src', '').hide();
    $('#ProfileImagePreviewContainer').addClass('d-none');
    $('#ProfileImageFileName').val('');
    $(this).addClass('d-none');
});
$('#editProfileImageBtn').on('click', function () {
    $('#editProfileImageInput').click();
});
// Edit image preview
$('#editProfileImageInput').on('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            $('#editProfileImagePreview').attr('src', e.target.result);
            $('#editProfileImagePreviewContainer').removeClass('d-none');
            $('#removeEditProfileImageBtn').removeClass('d-none');

            $('#editProfileImageFileName').val(file.name);
        };
        reader.readAsDataURL(file);
    }
});

// Remove button
$('#removeEditProfileImageBtn').click(function () {
    $('#editProfileImageInput').val('');
    $('#editProfileImagePreview').attr('src', '');
    $('#editProfileImagePreviewContainer').addClass('d-none');
    $('#editProfileImageFileName').val('');
    $(this).addClass('d-none');

});
