﻿(function ($) {
    'use strict';

    // Attach required field validation
    window.attachRequiredValidation = function (selector, message, type = "") {
        $(selector).attr('data-required', 'true');
        if (type) {
            $(selector).attr('data-type', type);
        }

        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        function getPasswordErrorMessage(password) {
            if (password.length < 8 || password.length > 12) {
                return "Password must be between 8 and 12 characters.";
            }
            if (!/[a-zA-Z]/.test(password)) {
                return "Password must include at least one letter.";
            }
            if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {
                return "Password must include at least one special character.";
            }
            if (!/[0-9]/.test(password)) {
                return "Password must include numbers.";
            }
            return "";
        }
        //if (selector === '#tinyMceExample' && tinymce.get('tinyMceExample')) {
        //    tinymce.get('tinyMceExample').on('blur', function () {
        //        const content = tinymce.get('tinyMceExample').getContent({ format: 'text' }).trim();
        //        const $textarea = $('#tinyMceExample');

        //        $textarea.removeClass('is-invalid');
        //        $textarea.nextAll('.invalid-feedback').remove();

        //        if (!content) {
        //            $textarea.addClass('is-invalid');
        //            $textarea.after(`<div class="invalid-feedback">${message}</div>`);
        //        }

        //        window.checkRequiredFields();
        //    });

        //    // Remove existing input listener if any (TinyMCE won't use it)
        //    return;
        //}
        // On blur validation
        $(document).on('blur', selector, function () {
            const $input = $(this);
            const value = $input.val().trim();
            $input.removeClass('is-invalid');
            $input.nextAll('.invalid-feedback').remove();

            if (value === '') {
                $input.addClass('is-invalid');
                $input.after(`<div class="invalid-feedback">${message}</div>`);
            } else if (type === 'email' && !isValidEmail(value)) {
                $input.addClass('is-invalid');
                $input.after(`<div class="invalid-feedback">Please enter a valid email</div>`);
            } else if (type === 'password') {
                const passwordError = getPasswordErrorMessage(value);
                if (passwordError) {
                    $input.addClass('is-invalid');
                    $input.after(`<div class="invalid-feedback">${passwordError}</div>`);
                }
            }

            window.checkRequiredFields();
        });

        // On input cleanup
        $(document).on('input', selector, function () {
            const $input = $(this);
            const value = $input.val().trim();
            if (value !== '') {
                $input.removeClass('is-invalid');
                $input.next('.invalid-feedback').remove();
            }
            window.checkRequiredFields();
        });
    };

    // Check if all required fields are filled
    window.checkRequiredFields = function () {
        let allFilled = true;
        const form = $('form'); // Adjust if needed

        form.find('[data-required="true"]').each(function () {
            const $field = $(this);
            const tag = $field.prop('tagName').toLowerCase();
            const type = $field.attr('type') || '';
            const value = $field.val();
        
            if (tag === 'select') {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                    allFilled = false;
                    return false;
                }
            } else if (type === 'checkbox' || type === 'radio') {
                if (!$field.is(':checked')) {
                    allFilled = false;
                    return false;
                }
            } else {
                if (!value || value.trim() === '') {
                    allFilled = false;
                    return false;
                }
            }
          
        });

        form.find('button[type="submit"]').prop('disabled', !allFilled);
    };
    window.validateTinyMCE = function (editor) {
        const content = editor.getContent({ format: 'text' }).trim();
        const $textarea = $(editor.getElement());
        $textarea.removeClass('is-invalid');
        $textarea.nextAll('.invalid-feedback').remove();

        if (content === '') {
            $textarea.addClass('is-invalid');
            $textarea.after(`<div class="invalid-feedback">Description is required.</div>`);
        }

        window.checkRequiredFields(); // Update submit button state
    };

})(jQuery);
